using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.AdminPortal.Models.Reviews;
using Blazored.LocalStorage;
using System.Net.Http.Headers;

namespace NafaPlace.AdminPortal.Services;

public class ReviewService : IReviewService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly JsonSerializerOptions _jsonOptions;

    public ReviewService(HttpClient httpClient, ILocalStorageService localStorage)
    {
        _httpClient = httpClient;
        _localStorage = localStorage;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<AdminReviewsPagedResponse> GetAdminReviewsAsync(AdminReviewFilterRequest request)
    {
        try
        {
            await SetAuthorizationHeader();
            
            var queryParams = new List<string>();
            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={request.Status}");
            if (request.Rating.HasValue)
                queryParams.Add($"rating={request.Rating}");
            if (request.IsVerifiedPurchase.HasValue)
                queryParams.Add($"isVerifiedPurchase={request.IsVerifiedPurchase}");
            if (!string.IsNullOrEmpty(request.DateFilter))
                queryParams.Add($"dateFilter={request.DateFilter}");
            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            if (!string.IsNullOrEmpty(request.SellerId))
                queryParams.Add($"sellerId={request.SellerId}");
            if (request.ProductId.HasValue)
                queryParams.Add($"productId={request.ProductId}");
            if (request.HasReports.HasValue)
                queryParams.Add($"hasReports={request.HasReports}");
            queryParams.Add($"page={request.Page}");
            queryParams.Add($"pageSize={request.PageSize}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"/api/moderation/reviews?{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<AdminReviewsPagedResponse>(content, _jsonOptions) ?? new AdminReviewsPagedResponse();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des avis admin: {ex.Message}");
        }

        // Return empty response instead of demo data
        return new AdminReviewsPagedResponse
        {
            Reviews = new List<AdminReviewDto>(),
            TotalCount = 0,
            TotalPages = 0,
            CurrentPage = request.Page,
            PageSize = request.PageSize
        };
    }

    public async Task<AdminReviewStatsDto> GetAdminReviewStatsAsync()
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync("/api/moderation/stats");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<AdminReviewStatsDto>(content, _jsonOptions) ?? new AdminReviewStatsDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques admin: {ex.Message}");
        }

        // Return empty stats instead of demo data
        return new AdminReviewStatsDto
        {
            TotalReviews = 0,
            PendingReviews = 0,
            ApprovedReviews = 0,
            RejectedReviews = 0,
            ReportedReviews = 0,
            AverageRating = 0,
            RatingDistribution = new Dictionary<int, int>
            {
                { 5, 0 },
                { 4, 0 },
                { 3, 0 },
                { 2, 0 },
                { 1, 0 }
            },
            ReviewsByPeriod = new Dictionary<string, int>
            {
                { "Cette semaine", 0 },
                { "Ce mois", 0 },
                { "Ce trimestre", 0 }
            }
        };
    }

    public async Task<AdminReviewDto?> GetReviewByIdAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/admin/{reviewId}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<AdminReviewDto>(content, _jsonOptions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de l'avis: {ex.Message}");
        }

        return null;
    }

    public async Task<bool> ApproveReviewAsync(int reviewId, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new ReviewModerationRequest
            {
                ReviewId = reviewId,
                IsApproved = true,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/admin/moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'approbation de l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> RejectReviewAsync(int reviewId, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new ReviewModerationRequest
            {
                ReviewId = reviewId,
                IsApproved = false,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/admin/moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du rejet de l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> DeleteReviewAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.DeleteAsync($"/api/reviews/admin/{reviewId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> BulkApproveReviewsAsync(List<int> reviewIds, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new BulkReviewModerationRequest
            {
                ReviewIds = reviewIds,
                IsApproved = true,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/admin/bulk-moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'approbation en lot: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> BulkRejectReviewsAsync(List<int> reviewIds, string? reason = null)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new BulkReviewModerationRequest
            {
                ReviewIds = reviewIds,
                IsApproved = false,
                Reason = reason
            };
            
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/admin/bulk-moderate", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du rejet en lot: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> BulkDeleteReviewsAsync(List<int> reviewIds)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/admin/bulk-delete", reviewIds);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression en lot: {ex.Message}");
            return false;
        }
    }

    public async Task<List<ReviewReportDto>> GetReviewReportsAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/admin/{reviewId}/reports");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ReviewReportDto>>(content, _jsonOptions) ?? new List<ReviewReportDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des signalements: {ex.Message}");
        }

        return new List<ReviewReportDto>();
    }

    public async Task<bool> ResolveReviewReportAsync(int reportId, string resolution)
    {
        try
        {
            await SetAuthorizationHeader();
            var request = new { Resolution = resolution };
            var response = await _httpClient.PostAsJsonAsync($"/api/reviews/admin/reports/{reportId}/resolve", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la résolution du signalement: {ex.Message}");
            return false;
        }
    }

    public async Task<ReviewAnalyticsDto> GetReviewAnalyticsAsync(string period = "month")
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/admin/analytics?period={period}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewAnalyticsDto>(content, _jsonOptions) ?? new ReviewAnalyticsDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des analytics: {ex.Message}");
        }

        return new ReviewAnalyticsDto();
    }

    private async Task SetAuthorizationHeader()
    {
        var token = await _localStorage.GetItemAsync<string>("authToken");
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }
    }



}
