using Microsoft.EntityFrameworkCore;
using NafaPlace.Delivery.Domain.Models;

namespace NafaPlace.Delivery.Infrastructure.Data;

public class DeliveryDbContext : DbContext
{
    public DeliveryDbContext(DbContextOptions<DeliveryDbContext> options) : base(options)
    {
    }

    public DbSet<DeliveryZone> DeliveryZones { get; set; }
    public DbSet<Carrier> Carriers { get; set; }
    public DbSet<DeliveryOrder> DeliveryOrders { get; set; }
    public DbSet<DeliveryTracking> DeliveryTrackings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // DeliveryZone configuration
        modelBuilder.Entity<DeliveryZone>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.BaseDeliveryFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.FreeDeliveryThreshold).HasColumnType("decimal(18,2)");
        });

        // Carrier configuration
        modelBuilder.Entity<Carrier>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ContactEmail).HasMaxLength(100);
            entity.Property(e => e.ContactPhone).HasMaxLength(20);
        });

        // DeliveryOrder configuration
        modelBuilder.Entity<DeliveryOrder>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OrderId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.TrackingNumber).IsRequired().HasMaxLength(50);
            entity.Property(e => e.RecipientName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.RecipientPhone).IsRequired().HasMaxLength(20);
            entity.Property(e => e.DeliveryAddress).IsRequired().HasMaxLength(500);
            entity.Property(e => e.DeliveryFee).HasColumnType("decimal(18,2)");
            entity.Property(e => e.OrderValue).HasColumnType("decimal(18,2)");

            entity.HasOne(e => e.Carrier)
                  .WithMany()
                  .HasForeignKey(e => e.CarrierId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Zone)
                  .WithMany()
                  .HasForeignKey(e => e.ZoneId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // DeliveryTracking configuration
        modelBuilder.Entity<DeliveryTracking>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).IsRequired();
            entity.Property(e => e.Location).HasMaxLength(200);
            entity.Property(e => e.Notes).HasMaxLength(500);

            entity.HasOne(e => e.DeliveryOrder)
                  .WithMany(d => d.TrackingEvents)
                  .HasForeignKey(e => e.DeliveryOrderId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Seed data
        SeedData(modelBuilder);
    }

    private void SeedData(ModelBuilder modelBuilder)
    {
        // Seed delivery zones for Guinea
        modelBuilder.Entity<DeliveryZone>().HasData(
            new DeliveryZone { Id = 1, Name = "Conakry Centre", Description = "Centre-ville de Conakry", BaseDeliveryFee = 15000, FreeDeliveryThreshold = 500000, IsActive = true, EstimatedDeliveryDays = 1 },
            new DeliveryZone { Id = 2, Name = "Conakry Périphérie", Description = "Banlieue de Conakry", BaseDeliveryFee = 25000, FreeDeliveryThreshold = 500000, IsActive = true, EstimatedDeliveryDays = 2 },
            new DeliveryZone { Id = 3, Name = "Kindia", Description = "Région de Kindia", BaseDeliveryFee = 50000, FreeDeliveryThreshold = 750000, IsActive = true, EstimatedDeliveryDays = 3 },
            new DeliveryZone { Id = 4, Name = "Boké", Description = "Région de Boké", BaseDeliveryFee = 60000, FreeDeliveryThreshold = 750000, IsActive = true, EstimatedDeliveryDays = 4 },
            new DeliveryZone { Id = 5, Name = "Labé", Description = "Région de Labé", BaseDeliveryFee = 70000, FreeDeliveryThreshold = 1000000, IsActive = true, EstimatedDeliveryDays = 5 }
        );

        // Seed carriers
        modelBuilder.Entity<Carrier>().HasData(
            new Carrier { Id = 1, Name = "NafaPlace Express", ContactEmail = "<EMAIL>", ContactPhone = "+************", IsActive = true },
            new Carrier { Id = 2, Name = "Guinée Livraison", ContactEmail = "<EMAIL>", ContactPhone = "+************", IsActive = true },
            new Carrier { Id = 3, Name = "Rapid Delivery GN", ContactEmail = "<EMAIL>", ContactPhone = "+************", IsActive = true }
        );
    }
}
