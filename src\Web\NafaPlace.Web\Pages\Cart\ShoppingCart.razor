@page "/cart"
@using NafaPlace.Web.Models.Cart
@using NafaPlace.Web.Services
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Authorization
@inject ICartService CartService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject IAuthService AuthService
@implements IDisposable

<PageTitle>Mon Panier - NafaPlace</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    Mon <PERSON>
                </h1>
                <button class="btn btn-outline-secondary" @onclick="ContinueShopping">
                    <i class="fas fa-arrow-left me-2"></i>
                    Continuer mes achats
                </button>
            </div>
        </div>
    </div>

    @if (_isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement...</span>
            </div>
            <p class="mt-3">Chargement de votre panier...</p>
        </div>
    }
    else if (_cart == null || !_cart.Items.Any())
    {
        <div class="row justify-content-center">
            <div class="col-md-6 text-center py-5">
                <div class="empty-cart-icon mb-4">
                    <i class="fas fa-shopping-cart fa-5x text-muted"></i>
                </div>
                <h3 class="text-muted mb-3">Votre panier est vide</h3>
                <p class="text-muted mb-4">Découvrez nos produits et ajoutez-les à votre panier pour commencer vos achats.</p>
                <button class="btn btn-primary btn-lg" @onclick="ContinueShopping">
                    <i class="fas fa-shopping-bag me-2"></i>
                    Découvrir nos produits
                </button>
            </div>
        </div>
    }
    else
    {
        <div class="row">
            <!-- Articles du panier -->
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Articles (@_cart.Items.Count)</h5>
                            <button class="btn btn-outline-danger btn-sm" @onclick="ClearCart" @onclick:preventDefault="true">
                                <i class="fas fa-trash me-1"></i>
                                Vider le panier
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        @foreach (var item in _cart.Items)
                        {
                            <div class="cart-item border-bottom p-4">
                                <div class="row align-items-center">
                                    <!-- Image du produit -->
                                    <div class="col-md-2 col-3">
                                        <div class="product-image">
                                            @if (!string.IsNullOrEmpty(item.ImageUrl))
                                            {
                                                <img src="@item.ImageUrl" alt="@item.ProductName" class="img-fluid rounded" style="max-height: 80px; object-fit: cover;" />
                                            }
                                            else
                                            {
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 80px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            }
                                        </div>
                                    </div>

                                    <!-- Détails du produit -->
                                    <div class="col-md-4 col-9">
                                        <h6 class="mb-1">@item.ProductName</h6>
                                        @if (!string.IsNullOrEmpty(item.VariantName))
                                        {
                                            <small class="text-muted">Variante: @item.VariantName</small>
                                        }
                                        <div class="mt-2">
                                            <span class="badge bg-success">En stock</span>
                                        </div>
                                    </div>

                                    <!-- Prix unitaire -->
                                    <div class="col-md-2 col-6 text-center">
                                        <div class="price">
                                            <strong>@item.UnitPrice.ToString("N0") GNF</strong>
                                        </div>
                                    </div>

                                    <!-- Contrôles de quantité -->
                                    <div class="col-md-2 col-6">
                                        <div class="quantity-controls">
                                            <div class="input-group input-group-sm">
                                                <button class="btn btn-outline-secondary" type="button"
                                                        @onclick="() => UpdateQuantity(item, item.Quantity - 1)"
                                                        disabled="@(item.Quantity <= 1)">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <input type="number" class="form-control text-center"
                                                       value="@item.Quantity" min="1" max="99"
                                                       @onchange="(e) => UpdateQuantityFromInput(item, e.Value?.ToString())" />
                                                <button class="btn btn-outline-secondary" type="button"
                                                        @onclick="() => UpdateQuantity(item, item.Quantity + 1)">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Total ligne et suppression -->
                                    <div class="col-md-2 col-12 text-end">
                                        <div class="line-total mb-2">
                                            <strong>@((item.UnitPrice * item.Quantity).ToString("N0")) GNF</strong>
                                        </div>
                                        <button class="btn btn-outline-danger btn-sm"
                                                @onclick="() => RemoveItem(item.ProductId)"
                                                title="Supprimer cet article">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Résumé de la commande -->
            <div class="col-lg-4">
                <div class="card shadow-sm sticky-top" style="top: 20px;">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            Résumé de la commande
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="order-summary">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Sous-total (@_cart.Items.Sum(i => i.Quantity) articles)</span>
                                <span>@GetSubTotal().ToString("N0") GNF</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Frais de livraison</span>
                                <span class="text-success">
                                    @if (GetSubTotal() > 500000)
                                    {
                                        <span>Gratuit</span>
                                    }
                                    else
                                    {
                                        <span>25 000 GNF</span>
                                    }
                                </span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>TVA (18%)</span>
                                <span>@GetTax().ToString("N0") GNF</span>
                            </div>

                            @if (_cart.CouponDiscount > 0)
                            {
                                <div class="d-flex justify-content-between mb-2 text-success">
                                    <span>
                                        <i class="bi bi-tag me-1"></i>Réduction (@_cart.CouponCode)
                                    </span>
                                    <span>-@_cart.CouponDiscount.ToString("N0") GNF</span>
                                </div>
                            }
                            <hr>
                            <div class="d-flex justify-content-between mb-3">
                                <strong>Total</strong>
                                <strong class="text-primary fs-5">@GetTotal().ToString("N0") GNF</strong>
                            </div>

                            @if (GetSubTotal() < 500000)
                            {
                                <div class="alert alert-info small mb-3">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Ajoutez @((500000 - GetSubTotal()).ToString("N0")) GNF pour bénéficier de la livraison gratuite !
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <!-- Composant Coupon -->
                <div class="mt-3">
                    <NafaPlace.Web.Shared.Components.CouponComponent
                        AppliedCouponCode="@_cart.CouponCode"
                        CouponDiscount="@_cart.CouponDiscount"
                        CouponDescription="@_cart.CouponDescription"
                        UserId="@_userId"
                        OnCouponChanged="LoadCart" />
                </div>

                <!-- Actions -->
                <div class="card mt-3">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" @onclick="Checkout">
                                <i class="fas fa-credit-card me-2"></i>
                                Passer la commande
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="ContinueShopping">
                                <i class="fas fa-shopping-bag me-2"></i>
                                Continuer mes achats
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Informations supplémentaires -->
                <div class="card shadow-sm mt-3">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-shield-alt me-2 text-success"></i>
                            Achat sécurisé
                        </h6>
                        <ul class="list-unstyled small mb-0">
                            <li><i class="fas fa-check text-success me-2"></i>Paiement sécurisé</li>
                            <li><i class="fas fa-check text-success me-2"></i>Livraison rapide</li>
                            <li><i class="fas fa-check text-success me-2"></i>Service client 24/7</li>
                            <li><i class="fas fa-check text-success me-2"></i>Garantie satisfait ou remboursé</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .cart-item:hover {
        background-color: #f8f9fa;
    }

    .quantity-controls .input-group {
        width: 120px;
    }

    .quantity-controls input {
        border-left: 0;
        border-right: 0;
    }

    .product-image img {
        transition: transform 0.2s;
    }

    .product-image img:hover {
        transform: scale(1.05);
    }

    .empty-cart-icon {
        opacity: 0.3;
    }

    .order-summary {
        font-size: 0.95rem;
    }

    @@media (max-width: 768px) {
        .quantity-controls .input-group {
            width: 100px;
        }

        .line-total {
            text-align: center !important;
        }
    }
</style>

@code {
    private CartDto? _cart;
    private string? _userId;
    private bool _isLoading = true;

    [CascadingParameter]
    private Task<AuthenticationState>? AuthenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await UpdateUserIdFromAuthState();
            await LoadCart();

            // S'abonner aux changements d'état d'authentification
            AuthService.AuthenticationStateChanged += OnAuthenticationStateChanged;
        }
        catch (Exception ex)
        {
            // Log error or show notification
            Console.WriteLine($"Erreur lors du chargement du panier: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task UpdateUserIdFromAuthState()
    {
        if (AuthenticationStateTask != null)
        {
            var authState = await AuthenticationStateTask;
            var user = authState.User;
            if (user.Identity?.IsAuthenticated == true)
            {
                _userId = user.FindFirst(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
                Console.WriteLine($"🔍 DEBUG ShoppingCart: UserId authentifié = '{_userId}'");
            }
            else
            {
                Console.WriteLine("🔍 DEBUG ShoppingCart: Utilisateur non authentifié");
                _userId = null;
            }
        }

        // Si l'utilisateur n'est pas connecté, utiliser l'ID invité
        if (string.IsNullOrEmpty(_userId))
        {
            _userId = await GetOrCreateGuestUserId();
            Console.WriteLine($"🔍 DEBUG ShoppingCart: ID invité = '{_userId}'");
        }
    }

    private async void OnAuthenticationStateChanged()
    {
        Console.WriteLine("🔔 DEBUG ShoppingCart: OnAuthenticationStateChanged déclenché !");
        await UpdateUserIdFromAuthState();
        await LoadCart();
        await InvokeAsync(StateHasChanged);
    }

    private async Task UpdateQuantity(CartItemDto item, int quantity)
    {
        if (string.IsNullOrEmpty(_userId)) return;

        try
        {
            if (quantity > 0 && quantity <= 99)
            {
                item.Quantity = quantity;
                _cart = await CartService.UpdateItemInCartAsync(_userId, item);
                StateHasChanged();
            }
            else if (quantity <= 0)
            {
                await RemoveItem(item.ProductId);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la mise à jour de la quantité: {ex.Message}");
            // Optionally show user notification
        }
    }

    private async Task UpdateQuantityFromInput(CartItemDto item, string? quantityStr)
    {
        if (int.TryParse(quantityStr, out int quantity))
        {
            await UpdateQuantity(item, quantity);
        }
    }

    private async Task RemoveItem(int productId)
    {
        if (string.IsNullOrEmpty(_userId)) return;

        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir supprimer cet article de votre panier ?");
            if (confirmed)
            {
                _cart = await CartService.RemoveItemFromCartAsync(_userId, productId);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression de l'article: {ex.Message}");
        }
    }

    private async Task ClearCart()
    {
        if (string.IsNullOrEmpty(_userId) || _cart?.Items?.Any() != true) return;

        try
        {
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Êtes-vous sûr de vouloir vider complètement votre panier ?");
            if (confirmed)
            {
                // Clear all items one by one (since we don't have a clear all endpoint in the current service)
                foreach (var item in _cart.Items.ToList())
                {
                    await CartService.RemoveItemFromCartAsync(_userId, item.ProductId);
                }
                _cart = await CartService.GetCartAsync(_userId);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du vidage du panier: {ex.Message}");
        }
    }

    private void Checkout()
    {
        if (_cart?.Items?.Any() == true)
        {
            NavigationManager.NavigateTo("/checkout");
        }
    }

    private void ContinueShopping()
    {
        NavigationManager.NavigateTo("/");
    }

    private async Task LoadCart()
    {
        try
        {
            if (!string.IsNullOrEmpty(_userId))
            {
                _cart = await CartService.GetCartAsync(_userId);
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du rechargement du panier: {ex.Message}");
        }
    }

    private decimal GetSubTotal()
    {
        return _cart?.Items?.Sum(i => i.UnitPrice * i.Quantity) ?? 0;
    }

    private decimal GetShippingFee()
    {
        var subTotal = GetSubTotal();
        return subTotal > 500000 ? 0 : 25000;
    }

    private decimal GetTax()
    {
        return GetSubTotal() * 0.18m;
    }

    private decimal GetTotal()
    {
        var subtotal = GetSubTotal();
        var discount = _cart?.CouponDiscount ?? 0;
        var discountedSubtotal = Math.Max(0, subtotal - discount);

        // Recalculer les frais de livraison sur le sous-total après réduction
        var shippingFee = discountedSubtotal > 500000 ? 0 : 25000;
        var tax = discountedSubtotal * 0.18m;

        return discountedSubtotal + shippingFee + tax;
    }

    private int GetItemCount()
    {
        return _cart?.Items?.Sum(i => i.Quantity) ?? 0;
    }

    private async Task<string> GetOrCreateGuestUserId()
    {
        // Utiliser le localStorage pour stocker l'ID de session invité
        var guestId = await JSRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");

        if (string.IsNullOrEmpty(guestId))
        {
            guestId = $"guest_{Guid.NewGuid():N}";
            await JSRuntime.InvokeVoidAsync("localStorage.setItem", "guestUserId", guestId);
        }

        return guestId;
    }

    public void Dispose()
    {
        AuthService.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
}
