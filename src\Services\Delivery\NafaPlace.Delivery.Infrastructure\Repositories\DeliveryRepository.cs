using Microsoft.EntityFrameworkCore;
using NafaPlace.Delivery.Application.Services;
using NafaPlace.Delivery.Application.DTOs;
using NafaPlace.Delivery.Domain.Models;
using NafaPlace.Delivery.Infrastructure.Data;

namespace NafaPlace.Delivery.Infrastructure.Repositories;

public class DeliveryRepository : IDeliveryRepository
{
    private readonly DeliveryDbContext _context;

    public DeliveryRepository(DeliveryDbContext context)
    {
        _context = context;
    }

    public async Task<List<DeliveryZoneDto>> GetDeliveryZonesAsync()
    {
        return await _context.DeliveryZones
            .Where(z => z.IsActive)
            .Select(z => new DeliveryZoneDto
            {
                Id = z.Id,
                Name = z.Name,
                Description = z.Description,
                BaseDeliveryFee = z.BaseDeliveryFee,
                FreeDeliveryThreshold = z.FreeDeliveryThreshold,
                EstimatedDeliveryDays = z.EstimatedDeliveryDays,
                IsActive = z.IsActive
            })
            .ToListAsync();
    }

    public async Task<DeliveryZoneDto?> GetDeliveryZoneByIdAsync(int zoneId)
    {
        var zone = await _context.DeliveryZones
            .Where(z => z.Id == zoneId && z.IsActive)
            .FirstOrDefaultAsync();

        if (zone == null) return null;

        return new DeliveryZoneDto
        {
            Id = zone.Id,
            Name = zone.Name,
            Description = zone.Description,
            BaseDeliveryFee = zone.BaseDeliveryFee,
            FreeDeliveryThreshold = zone.FreeDeliveryThreshold,
            EstimatedDeliveryDays = zone.EstimatedDeliveryDays,
            IsActive = zone.IsActive
        };
    }

    public async Task<DeliveryOrderDto> CreateDeliveryOrderAsync(CreateDeliveryOrderRequest request)
    {
        var deliveryOrder = new DeliveryOrder
        {
            OrderId = request.OrderId,
            TrackingNumber = GenerateTrackingNumber(),
            RecipientName = request.RecipientName,
            RecipientPhone = request.RecipientPhone,
            DeliveryAddress = request.DeliveryAddress,
            ZoneId = request.ZoneId,
            CarrierId = request.CarrierId ?? 1, // Default carrier
            DeliveryFee = request.DeliveryFee,
            OrderValue = request.OrderValue,
            Status = DeliveryStatus.Pending,
            CreatedAt = DateTime.UtcNow,
            EstimatedDeliveryDate = DateTime.UtcNow.AddDays(request.EstimatedDeliveryDays ?? 2)
        };

        _context.DeliveryOrders.Add(deliveryOrder);
        await _context.SaveChangesAsync();

        // Add initial tracking event
        var tracking = new DeliveryTracking
        {
            DeliveryOrderId = deliveryOrder.Id,
            Status = DeliveryStatus.Pending,
            Location = "Centre de tri",
            Notes = "Commande reçue et en cours de préparation",
            Timestamp = DateTime.UtcNow
        };

        _context.DeliveryTrackings.Add(tracking);
        await _context.SaveChangesAsync();

        return await GetDeliveryOrderByIdAsync(deliveryOrder.Id) ?? throw new InvalidOperationException("Failed to create delivery order");
    }

    public async Task<DeliveryOrderDto?> GetDeliveryOrderByIdAsync(int id)
    {
        var order = await _context.DeliveryOrders
            .Include(o => o.Zone)
            .Include(o => o.Carrier)
            .Include(o => o.TrackingEvents)
            .FirstOrDefaultAsync(o => o.Id == id);

        if (order == null) return null;

        return new DeliveryOrderDto
        {
            Id = order.Id,
            OrderId = order.OrderId,
            TrackingNumber = order.TrackingNumber,
            RecipientName = order.RecipientName,
            RecipientPhone = order.RecipientPhone,
            DeliveryAddress = order.DeliveryAddress,
            ZoneId = order.ZoneId,
            ZoneName = order.Zone?.Name,
            CarrierId = order.CarrierId,
            CarrierName = order.Carrier?.Name,
            DeliveryFee = order.DeliveryFee,
            OrderValue = order.OrderValue,
            Status = order.Status,
            CreatedAt = order.CreatedAt,
            EstimatedDeliveryDate = order.EstimatedDeliveryDate,
            ActualDeliveryDate = order.ActualDeliveryDate,
            TrackingEvents = order.TrackingEvents.Select(t => new DeliveryTrackingDto
            {
                Id = t.Id,
                Status = t.Status,
                Location = t.Location,
                Notes = t.Notes,
                Timestamp = t.Timestamp
            }).OrderByDescending(t => t.Timestamp).ToList()
        };
    }

    public async Task<List<DeliveryTrackingDto>> GetDeliveryTrackingByTrackingNumberAsync(string trackingNumber)
    {
        var order = await _context.DeliveryOrders
            .Include(o => o.TrackingEvents)
            .FirstOrDefaultAsync(o => o.TrackingNumber == trackingNumber);

        if (order == null) return new List<DeliveryTrackingDto>();

        return order.TrackingEvents.Select(t => new DeliveryTrackingDto
        {
            Id = t.Id,
            Status = t.Status,
            Location = t.Location,
            Notes = t.Notes,
            Timestamp = t.Timestamp
        }).OrderByDescending(t => t.Timestamp).ToList();
    }

    public async Task<bool> UpdateDeliveryStatusAsync(int deliveryOrderId, UpdateDeliveryStatusRequest request)
    {
        var order = await _context.DeliveryOrders.FindAsync(deliveryOrderId);
        if (order == null) return false;

        order.Status = request.Status;
        if (request.Status == DeliveryStatus.Delivered)
        {
            order.ActualDeliveryDate = DateTime.UtcNow;
        }

        // Add tracking event
        var tracking = new DeliveryTracking
        {
            DeliveryOrderId = deliveryOrderId,
            Status = request.Status,
            Location = request.Location ?? "",
            Notes = request.Notes ?? "",
            Timestamp = DateTime.UtcNow
        };

        _context.DeliveryTrackings.Add(tracking);
        await _context.SaveChangesAsync();

        return true;
    }

    private string GenerateTrackingNumber()
    {
        return $"NP{DateTime.UtcNow:yyyyMMdd}{Random.Shared.Next(1000, 9999)}";
    }
}
